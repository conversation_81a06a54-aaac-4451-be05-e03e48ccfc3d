# Phase 1: 基础架构搭建 - Augment Code 提示词

## 🎯 阶段目标
搭建Rust后端基础架构，确保数据库完全兼容，建立开发环境

## 📋 核心任务提示词

### Task 1.1: 开发环境搭建

```
请帮我搭建微博爬虫项目的Rust开发环境：

**项目背景**：
- 从Node.js + @nger框架迁移到Rust + Axum
- 需要保持与现有PostgreSQL数据库完全兼容
- 支持微博、知乎等平台的数据爬取

**具体需求**：
1. 创建标准的Rust项目结构
2. 配置Cargo.toml依赖清单，包含：
   - axum = "0.7" (Web框架)
   - sea-orm = "0.12" (数据库ORM)
   - tokio = "1.0" (异步运行时)
   - serde = "1.0" (序列化)
   - reqwest = "0.11" (HTTP客户端)
   - redis = "0.24" (Redis客户端)
   - lapin = "2.3" (RabbitMQ客户端)

3. 创建目录结构：
   ```
   src/
   ├── main.rs
   ├── config/
   ├── entities/
   ├── handlers/
   ├── services/
   ├── spider/
   └── tasks/
   ```

4. 配置开发工具：
   - rustfmt.toml (代码格式化)
   - clippy.toml (代码检查)
   - .gitignore

5. 编写Docker开发环境配置文件

**质量要求**：
- 遵循Rust最佳实践
- 代码格式化和检查通过
- 项目能够正常编译
```

### Task 1.2: 数据库连接层开发

```
请帮我实现微博爬虫项目的数据库连接层：

**项目背景**：
- 使用SeaORM连接PostgreSQL数据库
- 需要与现有数据库表结构完全兼容
- 支持连接池和事务管理

**具体需求**：
1. 实现数据库连接配置结构体：
   ```rust
   #[derive(Debug, Deserialize, Clone)]
   pub struct DatabaseConfig {
       pub url: String,
       pub max_connections: u32,
       pub min_connections: u32,
       pub connect_timeout: u64,
   }
   ```

2. 实现数据库连接池管理：
   - 使用SeaORM的连接池
   - 配置连接参数（最大连接数、超时时间等）
   - 实现健康检查接口

3. 实现数据库工具函数：
   - 连接建立函数
   - 健康检查函数
   - 事务管理函数

4. 错误处理：
   - 定义数据库错误类型
   - 实现错误转换和处理

**现有数据库表**：
- spilder_topic (话题表)
- spilder_topic_article (文章表)
- spilder_topic_article_comment (评论表)
- spilder_count_article (文章NLP分析表)

**质量要求**：
- 连接池性能优化
- 完整的错误处理
- 单元测试覆盖
```

### Task 1.3: 核心实体模型映射

```
请帮我实现微博爬虫项目的数据库实体模型映射：

**项目背景**：
- 使用SeaORM映射现有PostgreSQL表结构
- 必须保持与现有数据库完全兼容
- 不能修改现有表结构，只能添加新字段

**现有表结构分析**：
1. spilder_topic表字段：
   - id: SERIAL PRIMARY KEY
   - platform: VARCHAR(50) DEFAULT 'weibo'
   - title: VARCHAR(255) NOT NULL DEFAULT ''
   - uid: VARCHAR(50) NULLABLE
   - eid: INTEGER NULLABLE
   - summary: TEXT DEFAULT ''
   - target_url: VARCHAR(500) DEFAULT ''
   - read_count: BIGINT NULLABLE
   - mention_count: BIGINT NULLABLE
   - create_date: TIMESTAMPTZ
   - update_date: TIMESTAMPTZ

2. spilder_topic_article表字段：
   - id: SERIAL PRIMARY KEY
   - tid: INTEGER (关联spilder_topic.id)
   - eid: INTEGER (关联事件ID)
   - platform: VARCHAR(50) DEFAULT 'weibo'
   - text_raw: TEXT (文章内容)
   - reposts_count: BIGINT DEFAULT 0
   - comments_count: BIGINT DEFAULT 0
   - attitudes_count: BIGINT DEFAULT 0

**具体需求**：
1. 创建Topic实体模型 (src/entities/topic.rs)
2. 创建Article实体模型 (src/entities/article.rs)
3. 创建Comment实体模型 (src/entities/comment.rs)
4. 配置实体间关联关系
5. 确保字段类型和约束完全一致

**重要约束**：
- 字段名必须与数据库表完全一致
- 数据类型必须正确映射
- 默认值必须保持一致
- 不允许删除或修改现有字段

**质量要求**：
- 通过所有CRUD操作测试
- 关联查询正常工作
- 数据类型转换正确
```

### Task 1.4: 配置管理系统

```
请帮我实现微博爬虫项目的配置管理系统：

**项目背景**：
- 支持多环境配置（开发、测试、生产）
- 支持环境变量覆盖
- 统一管理数据库、Redis、RabbitMQ等配置

**具体需求**：
1. 设计配置结构体：
   ```rust
   #[derive(Debug, Deserialize, Clone)]
   pub struct Config {
       pub server: ServerConfig,
       pub database: DatabaseConfig,
       pub redis: RedisConfig,
       pub rabbitmq: RabbitMQConfig,
       pub spider: SpiderConfig,
   }
   ```

2. 实现配置加载机制：
   - 支持TOML配置文件
   - 支持环境变量覆盖
   - 支持配置验证

3. 创建配置文件模板：
   - config/default.toml
   - config/development.toml
   - config/production.toml

4. 实现配置管理功能：
   - 配置文件加载
   - 环境变量解析
   - 配置验证和错误处理
   - 配置热重载（可选）

**配置项包括**：
- 服务器配置（端口、工作线程数）
- 数据库配置（连接字符串、连接池参数）
- Redis配置（连接信息、缓存策略）
- RabbitMQ配置（连接信息、队列配置）
- 爬虫配置（请求间隔、重试次数、User-Agent列表）

**质量要求**：
- 配置验证完整
- 错误信息清晰
- 支持配置文档生成
```

### Task 1.5: Web服务器框架搭建

```
请帮我搭建微博爬虫项目的Web服务器框架：

**项目背景**：
- 使用Axum作为Web框架
- 需要支持RESTful API
- 集成中间件和错误处理

**具体需求**：
1. 实现基础Web服务器：
   ```rust
   // main.rs 结构
   #[tokio::main]
   async fn main() -> Result<(), Box<dyn std::error::Error>> {
       // 配置加载
       // 数据库连接
       // 路由配置
       // 服务器启动
   }
   ```

2. 配置路由结构：
   ```
   /api/
   ├── /health (健康检查)
   ├── /topics (话题管理)
   ├── /articles (文章管理)
   ├── /comments (评论管理)
   └── /analytics (数据分析)
   ```

3. 实现中间件：
   - CORS中间件
   - 请求日志中间件
   - 错误处理中间件
   - 认证中间件（预留）

4. 实现应用状态管理：
   ```rust
   #[derive(Clone)]
   pub struct AppState {
       pub db: DatabaseConnection,
       pub redis: redis::Client,
       pub config: Config,
   }
   ```

5. 实现基础API端点：
   - GET /api/health (健康检查)
   - 返回系统状态信息

**质量要求**：
- 服务器启动时间 < 5秒
- 健康检查响应时间 < 50ms
- 完整的错误处理机制
- 结构化日志记录
```

## 🔧 开发指南

### 代码质量标准
- 所有代码通过 `cargo fmt` 格式化
- 所有代码通过 `cargo clippy` 检查，无警告
- 单元测试覆盖率 ≥ 80%
- 所有公共API都有文档注释
- 错误处理完整，避免使用 `panic!`

### 性能要求
- 数据库连接池响应时间 < 10ms
- Web服务器启动时间 < 5s
- 健康检查接口响应时间 < 50ms

### 安全要求
- 使用参数化查询防止SQL注入
- 敏感配置信息使用环境变量
- 实现请求速率限制
- 添加输入验证和清理

## 📝 验收标准

### 功能验收
- [ ] 开发环境能够正常启动
- [ ] 数据库连接正常
- [ ] 实体模型映射正确
- [ ] 配置管理功能完整
- [ ] Web服务器正常运行

### 性能验收
- [ ] 数据库查询性能达标
- [ ] API响应时间符合要求
- [ ] 内存使用合理
- [ ] 并发处理能力达标

### 质量验收
- [ ] 代码格式化和检查通过
- [ ] 单元测试覆盖率达标
- [ ] 文档完整清晰
- [ ] 错误处理机制完善

## 🚨 注意事项

1. **数据库兼容性**：严格保持与现有数据库的兼容性，不能修改现有表结构
2. **性能优化**：注意数据库连接池配置和查询优化
3. **错误处理**：实现完整的错误处理机制，避免程序崩溃
4. **安全性**：注意输入验证和SQL注入防护
5. **可维护性**：保持代码结构清晰，添加必要的注释和文档
