# Phase 2: 核心功能开发 - Augment Code 提示词

## 🎯 阶段目标
重构爬虫引擎，迁移任务系统，实现核心业务逻辑

## 📋 核心任务提示词

### Task 2.1: 爬虫引擎架构设计

```
请帮我设计微博爬虫项目的现代化爬虫引擎架构：

**项目背景**：
- 支持多平台扩展（微博、知乎、今日头条等）
- 需要强大的反爬虫机制
- 支持分布式爬取和任务调度

**具体需求**：
1. 设计Spider trait接口：
   ```rust
   #[async_trait]
   pub trait Spider: Send + Sync {
       async fn init(&mut self) -> Result<(), SpiderError>;
       async fn login(&mut self, credentials: &LoginCredentials) -> Result<(), SpiderError>;
       async fn search_topics(&self, keyword: &str, page: u32) -> Result<Vec<TopicInfo>, SpiderError>;
       async fn get_topic_detail(&self, topic_url: &str) -> Result<TopicDetail, SpiderError>;
       async fn get_articles(&self, topic_id: &str, page: u32) -> Result<Vec<ArticleInfo>, SpiderError>;
       async fn get_comments(&self, article_id: &str, page: u32) -> Result<Vec<CommentInfo>, SpiderError>;
       async fn close(&mut self) -> Result<(), SpiderError>;
   }
   ```

2. 设计数据结构：
   - TopicInfo: 话题信息
   - ArticleInfo: 文章信息
   - CommentInfo: 评论信息
   - SpiderError: 爬虫错误类型

3. 设计反爬虫机制：
   - 代理池管理
   - User-Agent轮换
   - 请求频率控制
   - Cookie管理
   - 验证码处理

4. 设计爬虫配置：
   ```rust
   #[derive(Debug, Clone)]
   pub struct SpiderConfig {
       pub request_delay: Duration,
       pub max_retries: u32,
       pub timeout: Duration,
       pub user_agents: Vec<String>,
       pub proxy_list: Vec<String>,
   }
   ```

**质量要求**：
- 接口设计灵活，易于扩展
- 错误处理完整
- 支持异步操作
- 文档清晰完整
```

### Task 2.2: 微博爬虫实现

```
请帮我实现微博平台的爬虫功能：

**项目背景**：
- 实现Spider trait接口
- 支持话题搜索、文章抓取、评论获取
- 需要处理微博特有的反爬虫机制

**具体需求**：
1. 实现WeiboSpider结构体：
   ```rust
   pub struct WeiboSpider {
       client: Client,
       config: SpiderConfig,
       cookies: HashMap<String, String>,
       proxy_pool: ProxyPool,
   }
   ```

2. 实现核心功能：
   - search_topics: 搜索微博话题
   - get_topic_detail: 获取话题详情
   - get_articles: 获取话题下的文章列表
   - get_comments: 获取文章评论
   - get_child_comments: 获取子评论

3. 实现微博特有功能：
   - 登录状态管理
   - Cookie持久化
   - 微博URL解析
   - 数据清洗和提取

4. 反爬虫机制：
   - 模拟真实浏览器行为
   - 随机请求间隔
   - 处理验证码和限制

**微博API端点**：
- 话题搜索: https://s.weibo.com/topic
- 话题详情: https://weibo.com/p/100808{topic_id}
- 文章列表: https://weibo.com/ajax/statuses/mymblog
- 评论接口: https://weibo.com/ajax/statuses/buildComments

**数据提取要求**：
- 话题标题、描述、统计数据
- 文章内容、作者、发布时间、互动数据
- 评论内容、作者、发布时间、点赞数

**质量要求**：
- 数据提取准确率 ≥ 95%
- 反爬虫机制有效
- 错误处理完整
- 性能优化良好
```

### Task 2.3: 任务调度系统设计

```
请帮我设计基于RabbitMQ的分布式任务调度系统：

**项目背景**：
- 替代原有的任务系统
- 支持分布式任务处理
- 需要任务优先级和重试机制

**具体需求**：
1. 设计任务类型枚举：
   ```rust
   #[derive(Debug, Clone, Serialize, Deserialize)]
   pub enum TaskType {
       SearchTopics { keyword: String, platform: String, page: u32 },
       CrawlTopic { topic_url: String, platform: String },
       CrawlArticles { topic_id: String, platform: String, page: u32 },
       CrawlComments { article_id: String, platform: String, page: u32 },
       NlpAnalysis { content_id: String, content_type: String },
   }
   ```

2. 设计任务数据结构：
   ```rust
   #[derive(Debug, Clone, Serialize, Deserialize)]
   pub struct Task {
       pub id: Uuid,
       pub task_type: TaskType,
       pub priority: u8,
       pub max_retries: u8,
       pub current_retries: u8,
       pub created_at: DateTime<Utc>,
       pub scheduled_at: Option<DateTime<Utc>>,
       pub metadata: HashMap<String, String>,
   }
   ```

3. 设计队列结构：
   - 高优先级队列: spider.high
   - 普通优先级队列: spider.normal
   - 低优先级队列: spider.low
   - 死信队列: spider.dead

4. 设计任务状态跟踪：
   - PENDING: 待处理
   - PROCESSING: 处理中
   - COMPLETED: 已完成
   - FAILED: 失败
   - RETRY: 重试中

**质量要求**：
- 支持任务持久化
- 任务状态准确跟踪
- 支持任务取消和暂停
- 完整的错误处理
```

### Task 2.4: 任务调度器实现

```
请帮我实现任务调度器，负责任务的分发、监控和管理：

**项目背景**：
- 使用RabbitMQ作为消息队列
- 支持任务优先级和重试机制
- 需要监控任务执行状态

**具体需求**：
1. 实现TaskScheduler结构体：
   ```rust
   pub struct TaskScheduler {
       connection: Connection,
       channel: Channel,
       task_queues: HashMap<Priority, String>,
       dead_letter_queue: String,
   }
   ```

2. 实现核心功能：
   - schedule_task: 调度任务到队列
   - cancel_task: 取消任务
   - get_task_status: 获取任务状态
   - retry_failed_tasks: 重试失败任务

3. 实现队列管理：
   - 创建和配置队列
   - 设置死信队列
   - 配置消息持久化

4. 实现任务分发逻辑：
   - 根据优先级分发任务
   - 负载均衡算法
   - 任务去重机制

**RabbitMQ配置**：
- Exchange: spider.direct
- Routing Keys: high, normal, low
- Queue TTL: 24小时
- Message TTL: 1小时

**质量要求**：
- 任务分发准确
- 支持高并发
- 消息不丢失
- 性能优化良好
```

### Task 2.5: 任务执行器实现

```
请帮我实现任务执行器，负责从队列中获取任务并执行：

**项目背景**：
- 消费RabbitMQ队列中的任务
- 调用相应的爬虫执行具体操作
- 处理任务执行结果和错误

**具体需求**：
1. 实现TaskWorker结构体：
   ```rust
   pub struct TaskWorker {
       id: String,
       connection: Connection,
       channel: Channel,
       spider_factory: Arc<SpiderFactory>,
       db: DatabaseConnection,
   }
   ```

2. 实现任务消费逻辑：
   - 从队列中获取任务
   - 解析任务类型和参数
   - 调用相应的处理器

3. 实现任务处理器：
   - SearchTopicsHandler: 处理话题搜索任务
   - CrawlTopicHandler: 处理话题详情抓取
   - CrawlArticlesHandler: 处理文章抓取
   - CrawlCommentsHandler: 处理评论抓取

4. 实现结果处理：
   - 保存抓取结果到数据库
   - 创建后续任务
   - 更新任务状态
   - 错误处理和重试

**任务处理流程**：
1. 接收任务消息
2. 验证任务参数
3. 创建爬虫实例
4. 执行爬取操作
5. 保存结果数据
6. 确认消息处理完成

**质量要求**：
- 任务处理准确
- 错误处理完整
- 支持并发处理
- 资源使用合理
```

### Task 2.6: 数据服务层实现

```
请帮我实现数据访问服务层，提供统一的数据操作接口：

**项目背景**：
- 封装数据库操作逻辑
- 提供业务层接口
- 支持复杂查询和统计

**具体需求**：
1. 实现TopicService：
   ```rust
   pub struct TopicService {
       db: DatabaseConnection,
   }
   
   impl TopicService {
       pub async fn create_topic(&self, topic: CreateTopicRequest) -> Result<Topic, ServiceError>;
       pub async fn get_topics(&self, filter: TopicFilter) -> Result<(Vec<Topic>, u64), ServiceError>;
       pub async fn get_topic_by_id(&self, id: i32) -> Result<Option<Topic>, ServiceError>;
       pub async fn update_topic_stats(&self, id: i32, stats: TopicStats) -> Result<(), ServiceError>;
   }
   ```

2. 实现ArticleService：
   - 文章CRUD操作
   - 按话题查询文章
   - 文章统计信息
   - 批量操作支持

3. 实现CommentService：
   - 评论CRUD操作
   - 按文章查询评论
   - 评论层级关系处理
   - 评论统计信息

4. 实现AnalyticsService：
   - 数据统计查询
   - 趋势分析
   - 平台对比
   - 时间范围查询

**查询优化**：
- 使用索引优化查询性能
- 实现分页查询
- 支持复杂筛选条件
- 缓存热点数据

**质量要求**：
- 查询性能优化
- 事务管理完整
- 错误处理规范
- 接口设计合理
```

## 🔧 开发指南

### 爬虫开发最佳实践
- 遵守robots.txt协议
- 实现合理的请求频率控制
- 处理网站结构变化
- 实现数据验证和清洗
- 添加详细的日志记录

### 任务系统设计原则
- 任务幂等性保证
- 支持任务重试和回滚
- 实现任务优先级管理
- 监控任务执行状态
- 处理任务依赖关系

### 性能优化建议
- 使用连接池管理数据库连接
- 实现异步并发处理
- 优化数据库查询
- 使用缓存减少重复计算
- 监控系统资源使用

## 📝 验收标准

### 功能验收
- [ ] 爬虫能够正确获取目标数据
- [ ] 任务系统正常调度和执行
- [ ] 数据服务层接口完整
- [ ] 反爬虫机制有效

### 性能验收
- [ ] 爬虫处理速度达到预期
- [ ] 任务队列处理能力充足
- [ ] 数据库查询性能良好
- [ ] 系统资源使用合理

### 质量验收
- [ ] 代码质量符合标准
- [ ] 单元测试覆盖充分
- [ ] 错误处理机制完善
- [ ] 文档完整清晰

## 🚨 注意事项

1. **合规性**：确保爬虫行为符合法律法规和网站服务条款
2. **稳定性**：实现完善的错误处理和恢复机制
3. **扩展性**：设计支持新平台和新功能的扩展
4. **监控**：添加详细的日志和监控指标
5. **安全性**：保护用户数据和系统安全
