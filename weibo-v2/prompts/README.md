# 微博爬虫项目重构 - Augment Code 提示词集合

## 📋 项目概述

本项目是将基于Node.js + @nger框架的微博爬虫系统重构为Rust + React的现代化技术栈。项目分为5个阶段，每个阶段都有详细的Augment Code提示词指导开发工作。

## 🏗️ 技术架构

### 后端技术栈 (Rust)
- **Web框架**: Axum 0.7
- **数据库ORM**: SeaORM 0.12
- **异步运行时**: Tokio 1.0
- **HTTP客户端**: Reqwest 0.11
- **消息队列**: RabbitMQ (lapin 2.3)
- **缓存**: Redis 0.24
- **序列化**: Serde 1.0

### 前端技术栈 (React)
- **框架**: React 18 + TypeScript 5.0
- **样式**: TailwindCSS 3.3
- **状态管理**: React Query 5.0
- **路由**: React Router 6.8
- **表单**: React Hook Form 7.43
- **图表**: Recharts 2.5
- **构建工具**: Vite

## 📁 提示词文件结构

```
prompts/
├── README.md                           # 本文件 - 总览和使用指南
├── phase1-infrastructure-setup.md     # 阶段1: 基础架构搭建
├── phase2-core-functions.md          # 阶段2: 核心功能开发
├── phase3-frontend-development.md    # 阶段3: 前端开发
├── phase4-integration-testing.md     # 阶段4: 集成测试
└── phase5-deployment.md              # 阶段5: 部署上线
```

## 🚀 各阶段详细说明

### Phase 1: 基础架构搭建 (2周)
**文件**: `phase1-infrastructure-setup.md`

**主要任务**:
- 开发环境搭建和项目初始化
- 数据库连接层开发
- 核心实体模型映射（保持数据库兼容性）
- 配置管理系统实现
- Web服务器框架搭建

**关键特点**:
- 严格保持与现有PostgreSQL数据库的兼容性
- 使用SeaORM进行实体映射
- 实现统一的配置管理
- 建立标准的Rust项目结构

### Phase 2: 核心功能开发 (3.5周)
**文件**: `phase2-core-functions.md`

**主要任务**:
- 爬虫引擎架构设计和实现
- 微博、知乎等平台爬虫开发
- 基于RabbitMQ的任务调度系统
- 任务执行器和数据服务层
- 反爬虫机制实现

**关键特点**:
- 现代化的爬虫引擎设计
- 支持多平台扩展
- 分布式任务处理
- 强大的反爬虫能力

### Phase 3: 前端开发 (3周)
**文件**: `phase3-frontend-development.md`

**主要任务**:
- React项目初始化和工具链配置
- 基础UI组件库开发
- API客户端和自定义Hooks
- 仪表板、话题管理等核心页面
- 数据分析和可视化功能

**关键特点**:
- 现代化的React技术栈
- TypeScript类型安全
- 响应式设计
- 丰富的数据可视化

### Phase 4: 集成测试 (1.5周)
**文件**: `phase4-integration-testing.md`

**主要任务**:
- 测试环境搭建
- 单元测试和API集成测试
- 前端集成测试
- 爬虫功能和性能测试
- 数据一致性验证

**关键特点**:
- 全面的测试覆盖
- 自动化测试流程
- 性能基准验证
- 数据迁移验证

### Phase 5: 部署上线 (1周)
**文件**: `phase5-deployment.md`

**主要任务**:
- 生产环境准备和容器化部署
- 数据库迁移和蓝绿部署
- 监控系统搭建
- 运维文档编写和团队培训

**关键特点**:
- 零停机部署
- 完善的监控体系
- 详细的运维文档
- 团队知识传承

## 📖 使用指南

### 如何使用这些提示词

1. **按阶段顺序执行**: 严格按照Phase 1-5的顺序进行开发
2. **详细阅读提示词**: 每个提示词都包含详细的需求说明和代码示例
3. **保持数据库兼容**: 特别注意Phase 1中的数据库兼容性要求
4. **质量标准遵循**: 每个阶段都有明确的质量要求和验收标准

### 提示词使用模板

```
我正在进行微博爬虫项目的重构工作，当前处于 [阶段名称]。

请根据 prompts/[对应文件名].md 中的 [具体任务名称] 提示词，帮我实现以下功能：

[具体需求描述]

请确保：
1. 严格遵循提示词中的技术要求
2. 保持代码质量标准
3. 实现完整的错误处理
4. 添加必要的测试用例
```

### 开发环境要求

**Rust环境**:
```bash
# 安装Rust工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup update stable
rustup component add rustfmt clippy
```

**Node.js环境**:
```bash
# 安装Node.js 18+
nvm install 18
nvm use 18
npm install -g pnpm
```

**数据库环境**:
```bash
# 使用Docker启动开发环境
docker-compose -f docker-compose.dev.yml up -d
```

## 🎯 项目目标

### 技术目标
- **性能提升**: 内存使用降低60%，响应速度提升50%
- **现代化**: 采用Rust + React现代技术栈
- **类型安全**: 全栈TypeScript/Rust类型安全
- **可维护性**: 清晰的架构分层和代码组织

### 业务目标
- **功能完整**: 100%保持现有功能
- **数据兼容**: 零数据丢失，完全兼容现有数据库
- **用户体验**: 界面现代化，操作更流畅
- **扩展能力**: 支持更多平台和数据源

## ⚠️ 重要注意事项

### 数据库兼容性
- **严格保持兼容**: 不能修改现有表结构
- **只增不减**: 只能添加新字段，不能删除现有字段
- **类型一致**: 字段类型和约束必须完全一致
- **关系保持**: 外键关系和索引结构不变

### 开发规范
- **代码质量**: 遵循Rust和TypeScript最佳实践
- **测试覆盖**: 单元测试覆盖率≥80%
- **文档完整**: 所有公共API都有文档注释
- **错误处理**: 完整的错误处理机制

### 安全要求
- **输入验证**: 所有用户输入都要验证
- **SQL注入防护**: 使用参数化查询
- **认证授权**: 实现完整的认证机制
- **数据加密**: 敏感数据加密存储

## 📊 项目进度跟踪

### 总体时间线
- **Phase 1**: 2周 (基础架构)
- **Phase 2**: 3.5周 (核心功能)
- **Phase 3**: 3周 (前端开发)
- **Phase 4**: 1.5周 (集成测试)
- **Phase 5**: 1周 (部署上线)
- **总计**: 11周

### 关键里程碑
- **Week 2**: 基础架构完成，数据库连接正常
- **Week 5.5**: 核心功能完成，爬虫系统可用
- **Week 8.5**: 前端开发完成，用户界面可用
- **Week 10**: 集成测试完成，系统功能验证
- **Week 11**: 系统正式上线，项目交付完成

## 🤝 团队协作

### 角色分工
- **后端开发工程师** × 2: 负责Rust后端开发
- **前端开发工程师** × 2: 负责React前端开发
- **爬虫工程师** × 1: 负责爬虫引擎开发
- **测试工程师** × 2: 负责测试用例编写和执行
- **DevOps工程师** × 1: 负责部署和运维
- **UI/UX设计师** × 1: 负责界面设计

### 沟通协作
- **每日站会**: 同步进度和问题
- **周度评审**: 阶段成果评审
- **技术分享**: 关键技术点分享
- **代码评审**: 所有代码都要经过评审

## 📞 支持和反馈

如果在使用这些提示词过程中遇到问题，请：

1. 仔细阅读对应阶段的详细提示词
2. 检查是否满足前置依赖条件
3. 参考项目的技术文档和最佳实践
4. 与团队成员讨论技术方案

祝您重构项目顺利！🚀
