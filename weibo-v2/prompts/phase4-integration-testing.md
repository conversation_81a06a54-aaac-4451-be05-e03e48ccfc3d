# Phase 4: 集成测试 - Augment Code 提示词

## 🎯 阶段目标
进行全面的系统集成测试，确保功能完整性和性能达标

## 📋 核心任务提示词

### Task 4.1: 测试环境搭建

```
请帮我搭建微博爬虫项目的完整测试环境：

**项目背景**：
- 需要与生产环境配置一致的测试环境
- 支持自动化测试和手动测试
- 包含完整的数据库、缓存、消息队列等基础设施

**具体需求**：
1. Docker测试环境配置 (docker-compose.test.yml)：
   ```yaml
   version: '3.8'
   services:
     postgres-test:
       image: postgres:15
       environment:
         POSTGRES_DB: spider_test
         POSTGRES_USER: test_user
         POSTGRES_PASSWORD: test_pass
       ports:
         - "5433:5432"
     
     redis-test:
       image: redis:7-alpine
       ports:
         - "6380:6379"
     
     rabbitmq-test:
       image: rabbitmq:3-management
       ports:
         - "5673:5672"
         - "15673:15672"
   ```

2. 测试数据准备：
   - 创建测试数据生成脚本
   - 准备各种场景的测试数据
   - 实现数据清理和重置功能

3. 测试配置管理：
   - config/test.toml 配置文件
   - 环境变量配置
   - 测试专用的数据库连接

4. 自动化测试工具配置：
   - Rust: cargo test 配置
   - 前端: Jest + React Testing Library
   - API测试: 使用reqwest或类似工具
   - 性能测试: 使用criterion或类似工具

**测试数据要求**：
- 覆盖各种业务场景
- 包含边界条件数据
- 支持并发测试
- 数据一致性保证

**质量要求**：
- 测试环境启动时间 < 30秒
- 测试数据生成完整
- 环境隔离性良好
- 支持并行测试
```

### Task 4.2: 单元测试补充

```
请帮我补充和完善微博爬虫项目的单元测试：

**项目背景**：
- 确保代码覆盖率达到85%以上
- 覆盖所有核心业务逻辑
- 包含边界条件和异常情况测试

**具体需求**：
1. 实体模型测试 (tests/entities/)：
   ```rust
   #[cfg(test)]
   mod tests {
       use super::*;
       use sea_orm::*;
       
       #[tokio::test]
       async fn test_topic_creation() {
           let db = setup_test_db().await;
           let topic = topic::ActiveModel {
               platform: Set("weibo".to_string()),
               title: Set("测试话题".to_string()),
               ..Default::default()
           };
           
           let result = topic.insert(&db).await;
           assert!(result.is_ok());
       }
   }
   ```

2. 服务层测试 (tests/services/)：
   - TopicService单元测试
   - ArticleService单元测试
   - CommentService单元测试
   - AnalyticsService单元测试

3. 爬虫引擎测试 (tests/spider/)：
   - Spider trait实现测试
   - 数据提取准确性测试
   - 反爬虫机制测试
   - 错误处理测试

4. 任务系统测试 (tests/tasks/)：
   - 任务调度器测试
   - 任务执行器测试
   - 队列管理测试
   - 重试机制测试

5. API处理器测试 (tests/handlers/)：
   - HTTP请求处理测试
   - 参数验证测试
   - 响应格式测试
   - 错误处理测试

**测试工具配置**：
- 使用tokio-test进行异步测试
- 使用mockall进行依赖模拟
- 使用testcontainers进行集成测试
- 使用criterion进行性能测试

**质量要求**：
- 单元测试覆盖率 ≥ 85%
- 所有测试用例通过
- 测试执行时间合理
- 测试报告清晰完整
```

### Task 4.3: API集成测试

```
请帮我进行微博爬虫项目的API集成测试：

**项目背景**：
- 验证所有API接口功能正确性
- 测试API性能和响应时间
- 确保API文档与实现一致

**具体需求**：
1. 话题管理API测试：
   ```rust
   #[tokio::test]
   async fn test_topic_crud_operations() {
       let app = setup_test_app().await;
       
       // 测试创建话题
       let create_request = CreateTopicRequest {
           platform: "weibo".to_string(),
           keyword: "测试关键词".to_string(),
       };
       
       let response = app
           .oneshot(
               Request::builder()
                   .method("POST")
                   .uri("/api/topics")
                   .header("content-type", "application/json")
                   .body(serde_json::to_string(&create_request).unwrap().into())
                   .unwrap(),
           )
           .await
           .unwrap();
       
       assert_eq!(response.status(), StatusCode::CREATED);
   }
   ```

2. 文章管理API测试：
   - 获取文章列表
   - 文章详情查询
   - 文章搜索功能
   - 文章统计信息

3. 评论管理API测试：
   - 获取评论列表
   - 评论层级关系
   - 评论统计信息
   - 评论搜索功能

4. 统计分析API测试：
   - 仪表板统计数据
   - 趋势分析数据
   - 平台对比数据
   - 时间范围查询

5. 用户认证API测试：
   - 登录功能测试
   - Token验证测试
   - 权限检查测试
   - 登出功能测试

**性能测试要求**：
- API响应时间 < 200ms (95%请求)
- 支持1000并发请求
- 内存使用稳定
- 无内存泄漏

**质量要求**：
- 所有API接口功能正确
- 错误处理机制完整
- 响应格式标准化
- 文档与实现一致
```

### Task 4.4: 前端集成测试

```
请帮我进行微博爬虫项目的前端集成测试：

**项目背景**：
- 验证前端页面功能完整性
- 测试用户交互流程
- 确保响应式设计正常

**具体需求**：
1. 页面渲染测试：
   ```tsx
   import { render, screen } from '@testing-library/react';
   import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
   import { Dashboard } from '../pages/Dashboard';
   
   describe('Dashboard Page', () => {
     test('renders dashboard statistics', async () => {
       const queryClient = new QueryClient({
         defaultOptions: { queries: { retry: false } },
       });
       
       render(
         <QueryClientProvider client={queryClient}>
           <Dashboard />
         </QueryClientProvider>
       );
       
       expect(screen.getByText('总话题数')).toBeInTheDocument();
       expect(screen.getByText('总文章数')).toBeInTheDocument();
     });
   });
   ```

2. 用户交互测试：
   - 表单提交测试
   - 按钮点击测试
   - 搜索功能测试
   - 分页功能测试

3. 数据展示测试：
   - 表格数据渲染
   - 图表数据展示
   - 加载状态显示
   - 错误状态处理

4. 路由导航测试：
   - 页面跳转功能
   - 路由参数传递
   - 面包屑导航
   - 404页面处理

5. 响应式设计测试：
   - 移动端布局测试
   - 平板端布局测试
   - 桌面端布局测试
   - 断点切换测试

**测试工具配置**：
- Jest + React Testing Library
- MSW (Mock Service Worker) 模拟API
- @testing-library/user-event 用户交互
- jest-axe 无障碍访问测试

**质量要求**：
- 组件测试覆盖率 ≥ 80%
- 用户交互流程完整
- 响应式设计正常
- 无障碍访问支持
```

### Task 4.5: 爬虫功能测试

```
请帮我进行微博爬虫项目的爬虫功能测试：

**项目背景**：
- 验证爬虫数据采集功能
- 测试反爬虫机制有效性
- 确保数据提取准确性

**具体需求**：
1. 微博爬虫测试：
   ```rust
   #[tokio::test]
   async fn test_weibo_spider_functionality() {
       let config = SpiderConfig::default();
       let mut spider = WeiboSpider::new(config);
       
       // 测试初始化
       spider.init().await.expect("Spider initialization failed");
       
       // 测试话题搜索
       let topics = spider
           .search_topics("测试关键词", 1)
           .await
           .expect("Topic search failed");
       
       assert!(!topics.is_empty());
       assert!(topics[0].title.contains("测试关键词"));
   }
   ```

2. 知乎爬虫测试：
   - 问题搜索功能
   - 问题详情获取
   - 回答列表抓取
   - 评论数据获取

3. 反爬虫机制测试：
   - 请求频率控制
   - User-Agent轮换
   - 代理池使用
   - Cookie管理

4. 数据提取准确性测试：
   - 文本内容提取
   - 数值数据提取
   - 时间格式解析
   - URL链接提取

5. 错误处理测试：
   - 网络错误处理
   - 页面结构变化
   - 反爬虫触发处理
   - 数据解析错误

**测试数据验证**：
- 数据完整性检查
- 数据格式验证
- 重复数据检测
- 数据质量评估

**质量要求**：
- 数据提取准确率 ≥ 95%
- 反爬虫机制有效
- 错误处理完整
- 性能表现良好
```

### Task 4.6: 性能测试

```
请帮我进行微博爬虫项目的性能测试：

**项目背景**：
- 验证系统在高负载下的表现
- 识别性能瓶颈和优化点
- 确保系统稳定性

**具体需求**：
1. API性能测试：
   ```rust
   use criterion::{black_box, criterion_group, criterion_main, Criterion};
   
   fn benchmark_topic_api(c: &mut Criterion) {
       let rt = tokio::runtime::Runtime::new().unwrap();
       let app = rt.block_on(setup_test_app());
       
       c.bench_function("get_topics", |b| {
           b.iter(|| {
               rt.block_on(async {
                   let response = app
                       .clone()
                       .oneshot(
                           Request::builder()
                               .uri("/api/topics")
                               .body(Body::empty())
                               .unwrap(),
                       )
                       .await
                       .unwrap();
                   
                   black_box(response);
               })
           })
       });
   }
   ```

2. 数据库性能测试：
   - 查询响应时间测试
   - 并发连接测试
   - 大数据量查询测试
   - 索引效果验证

3. 爬虫并发性能测试：
   - 并发爬取能力
   - 资源使用情况
   - 内存泄漏检测
   - 长时间运行稳定性

4. 前端性能测试：
   - 页面加载时间
   - 首屏渲染时间
   - 交互响应时间
   - 内存使用情况

5. 系统整体性能测试：
   - 端到端响应时间
   - 系统吞吐量
   - 资源利用率
   - 扩展性测试

**性能指标要求**：
- API响应时间 < 200ms (95%请求)
- 数据库查询时间 < 100ms
- 页面加载时间 < 3秒
- 系统支持1000并发用户

**质量要求**：
- 性能测试覆盖全面
- 测试结果准确可靠
- 性能报告详细清晰
- 优化建议具体可行
```

### Task 4.7: 数据一致性测试

```
请帮我进行微博爬虫项目的数据一致性测试：

**项目背景**：
- 验证新旧系统数据一致性
- 确保数据迁移完整性
- 检查数据关联关系正确性

**具体需求**：
1. 数据结构对比测试：
   ```rust
   #[tokio::test]
   async fn test_data_structure_consistency() {
       let old_db = setup_old_database().await;
       let new_db = setup_new_database().await;
       
       // 对比表结构
       let old_schema = get_table_schema(&old_db, "spilder_topic").await;
       let new_schema = get_table_schema(&new_db, "spilder_topic").await;
       
       assert_eq!(old_schema.columns.len(), new_schema.columns.len());
       
       for (old_col, new_col) in old_schema.columns.iter().zip(new_schema.columns.iter()) {
           assert_eq!(old_col.name, new_col.name);
           assert_eq!(old_col.data_type, new_col.data_type);
       }
   }
   ```

2. 数据完整性验证：
   - 记录数量对比
   - 主键完整性检查
   - 外键关联验证
   - 数据类型一致性

3. 数据内容验证：
   - 关键字段内容对比
   - 统计数据准确性
   - 时间戳格式验证
   - 文本内容完整性

4. 关联关系验证：
   - 话题-文章关联
   - 文章-评论关联
   - 用户-内容关联
   - 统计数据关联

5. 数据迁移验证：
   - 迁移前后数据对比
   - 迁移过程完整性
   - 回滚机制验证
   - 增量更新测试

**验证工具**：
- 数据对比脚本
- 统计查询验证
- 数据完整性检查
- 自动化验证流程

**质量要求**：
- 数据迁移完整性 100%
- 数据类型转换正确
- 关联关系完整
- 统计数据准确
```

## 🔧 测试指南

### 测试策略
- 单元测试：测试独立功能模块
- 集成测试：测试模块间交互
- 端到端测试：测试完整业务流程
- 性能测试：验证系统性能指标
- 安全测试：检查安全漏洞

### 测试数据管理
- 使用测试专用数据库
- 实现数据隔离和清理
- 准备多样化测试数据
- 支持并行测试执行

### 自动化测试
- 集成CI/CD流程
- 自动化测试执行
- 测试报告生成
- 失败测试通知

## 📝 验收标准

### 功能验收
- [ ] 所有功能测试通过
- [ ] API接口正常工作
- [ ] 前端页面功能完整
- [ ] 爬虫数据采集正确

### 性能验收
- [ ] API响应时间达标
- [ ] 数据库查询性能良好
- [ ] 前端页面加载快速
- [ ] 系统并发能力充足

### 质量验收
- [ ] 测试覆盖率达标
- [ ] 数据一致性验证通过
- [ ] 错误处理机制完善
- [ ] 系统稳定性良好

## 🚨 注意事项

1. **测试环境隔离**：确保测试不影响生产数据
2. **数据安全**：保护测试数据安全和隐私
3. **性能基准**：建立性能基准和监控
4. **回归测试**：确保修复不引入新问题
5. **文档更新**：及时更新测试文档和报告
