# Phase 3: 前端开发 - Augment Code 提示词

## 🎯 阶段目标
开发现代化的React前端界面，提供优秀的用户体验

## 📋 核心任务提示词

### Task 3.1: 前端项目初始化

```
请帮我初始化微博爬虫项目的React前端：

**项目背景**：
- 使用React 18 + TypeScript + TailwindCSS
- 需要现代化的开发工具链
- 支持响应式设计和组件化开发

**具体需求**：
1. 创建React + TypeScript项目：
   ```bash
   npm create vite@latest spider-frontend -- --template react-ts
   ```

2. 安装核心依赖：
   ```json
   {
     "dependencies": {
       "react": "^18.2.0",
       "react-dom": "^18.2.0",
       "typescript": "^5.0.0",
       "tailwindcss": "^3.3.0",
       "@tanstack/react-query": "^5.0.0",
       "@tanstack/react-table": "^8.10.0",
       "react-router-dom": "^6.8.0",
       "react-hook-form": "^7.43.0",
       "zod": "^3.20.0",
       "recharts": "^2.5.0",
       "lucide-react": "^0.263.0",
       "axios": "^1.6.0"
     }
   }
   ```

3. 配置开发工具：
   - ESLint + Prettier代码规范
   - TailwindCSS样式框架
   - Vite构建工具配置
   - TypeScript配置优化

4. 创建项目目录结构：
   ```
   src/
   ├── components/
   │   ├── ui/
   │   ├── layout/
   │   ├── forms/
   │   └── charts/
   ├── pages/
   ├── hooks/
   ├── services/
   ├── types/
   ├── utils/
   └── styles/
   ```

5. 配置环境变量管理：
   - .env.development
   - .env.production
   - 环境变量类型定义

**质量要求**：
- 项目能够正常启动和热重载
- 代码格式化和检查工具正常
- TypeScript类型检查通过
- 构建流程正常
```

### Task 3.2: 基础UI组件库开发

```
请帮我开发微博爬虫项目的基础UI组件库：

**项目背景**：
- 基于TailwindCSS构建
- 支持TypeScript类型检查
- 遵循现代化设计规范

**具体需求**：
1. Button组件 (src/components/ui/Button.tsx)：
   ```tsx
   interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
     variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
     size?: 'sm' | 'md' | 'lg';
     loading?: boolean;
     icon?: React.ReactNode;
   }
   ```

2. Input组件 (src/components/ui/Input.tsx)：
   - 支持不同类型（text, email, password, search）
   - 错误状态显示
   - 前缀和后缀图标
   - 清除按钮

3. Table组件 (src/components/ui/Table.tsx)：
   - 支持排序功能
   - 分页组件集成
   - 行选择功能
   - 加载状态
   - 空数据状态

4. Modal组件 (src/components/ui/Modal.tsx)：
   - 支持不同尺寸
   - 可拖拽功能
   - 键盘事件处理
   - 焦点管理

5. Select组件 (src/components/ui/Select.tsx)：
   - 单选和多选支持
   - 搜索功能
   - 异步数据加载
   - 自定义选项渲染

**设计要求**：
- 遵循无障碍访问标准
- 支持暗色主题
- 响应式设计
- 动画效果流畅

**质量要求**：
- 组件API设计合理
- TypeScript类型定义完整
- 支持ref转发
- 文档和示例完整
```

### Task 3.3: API客户端开发

```
请帮我开发微博爬虫项目的API客户端：

**项目背景**：
- 与Rust后端API通信
- 需要类型安全的接口调用
- 支持认证和错误处理

**具体需求**：
1. 配置Axios客户端 (src/services/api.ts)：
   ```tsx
   class ApiClient {
     private client: AxiosInstance;
     
     constructor() {
       this.client = axios.create({
         baseURL: import.meta.env.VITE_API_URL,
         timeout: 10000,
       });
       this.setupInterceptors();
     }
   }
   ```

2. 定义API响应类型：
   ```tsx
   export interface ApiResponse<T> {
     success: boolean;
     data: T;
     message: string;
     total?: number;
   }
   
   export interface Topic {
     id: number;
     platform: string;
     title: string;
     summary: string;
     readCount: number;
     mentionCount: number;
     createdDate: string;
   }
   ```

3. 实现API方法：
   - getTopics: 获取话题列表
   - createTopic: 创建话题
   - getArticles: 获取文章列表
   - getComments: 获取评论列表
   - getDashboardStats: 获取仪表板统计

4. 实现请求/响应拦截器：
   - 自动添加认证token
   - 统一错误处理
   - 请求重试机制
   - 加载状态管理

**错误处理**：
- 网络错误处理
- HTTP状态码处理
- 业务错误处理
- 用户友好的错误提示

**质量要求**：
- API调用类型安全
- 错误处理完整
- 支持请求取消
- 性能优化良好
```

### Task 3.4: 自定义Hooks开发

```
请帮我开发微博爬虫项目的自定义React Hooks：

**项目背景**：
- 封装常用的业务逻辑
- 提供可复用的状态管理
- 集成React Query进行数据获取

**具体需求**：
1. useApi Hook (src/hooks/useApi.ts)：
   ```tsx
   export const useTopics = (params?: TopicQueryParams) => {
     return useQuery({
       queryKey: ['topics', params],
       queryFn: () => apiClient.getTopics(params),
       staleTime: 5 * 60 * 1000,
     });
   };
   
   export const useCreateTopic = () => {
     const queryClient = useQueryClient();
     return useMutation({
       mutationFn: apiClient.createTopic,
       onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ['topics'] });
       },
     });
   };
   ```

2. useAuth Hook (src/hooks/useAuth.ts)：
   - 用户认证状态管理
   - 登录/登出功能
   - Token管理
   - 权限检查

3. usePagination Hook (src/hooks/usePagination.ts)：
   - 分页状态管理
   - 页码计算
   - 分页参数生成
   - 跳转功能

4. useDebounce Hook (src/hooks/useDebounce.ts)：
   - 防抖处理
   - 搜索优化
   - 性能提升

5. useLocalStorage Hook (src/hooks/useLocalStorage.ts)：
   - 本地存储管理
   - 类型安全
   - 同步状态

**质量要求**：
- Hooks设计合理
- TypeScript类型完整
- 性能优化良好
- 测试覆盖充分
```

### Task 3.5: 仪表板页面开发

```
请帮我开发微博爬虫项目的仪表板页面：

**项目背景**：
- 展示系统关键统计数据
- 提供数据可视化图表
- 支持实时数据更新

**具体需求**：
1. 页面布局设计 (src/pages/Dashboard.tsx)：
   ```tsx
   export const Dashboard: React.FC = () => {
     const { data: stats } = useDashboardStats();
     
     return (
       <div className="space-y-6">
         <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
           {/* 统计卡片 */}
         </div>
         <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
           {/* 图表区域 */}
         </div>
       </div>
     );
   };
   ```

2. 统计卡片组件：
   - 总话题数
   - 总文章数
   - 总评论数
   - 今日任务数

3. 图表组件集成：
   - 使用Recharts库
   - 平台数据分布柱状图
   - 时间趋势折线图
   - 情感分析饼图

4. 实时活动列表：
   - 最近爬取的话题
   - 任务执行状态
   - 系统事件日志

5. 响应式设计：
   - 移动端适配
   - 平板端优化
   - 桌面端布局

**数据更新**：
- 30秒自动刷新
- 手动刷新按钮
- WebSocket实时更新（可选）

**质量要求**：
- 页面加载速度快
- 图表交互流畅
- 数据展示准确
- 用户体验良好
```

### Task 3.6: 话题管理页面开发

```
请帮我开发微博爬虫项目的话题管理页面：

**项目背景**：
- 管理爬取的话题数据
- 支持搜索、筛选、创建等操作
- 提供批量操作功能

**具体需求**：
1. 页面主体结构 (src/pages/Topics.tsx)：
   ```tsx
   export const Topics: React.FC = () => {
     const [filters, setFilters] = useState<TopicFilters>({});
     const { data: topicsData } = useTopics(filters);
     const createTopic = useCreateTopic();
     
     return (
       <div className="space-y-6">
         <TopicHeader onCreateTopic={handleCreateTopic} />
         <TopicFilters filters={filters} onChange={setFilters} />
         <TopicTable data={topicsData} />
       </div>
     );
   };
   ```

2. 话题列表表格：
   - 支持排序（按时间、阅读数、讨论数）
   - 分页功能
   - 行选择功能
   - 操作按钮（查看、编辑、删除）

3. 搜索和筛选功能：
   - 关键词搜索
   - 平台筛选
   - 时间范围筛选
   - 状态筛选

4. 话题创建对话框：
   - 表单验证
   - 平台选择
   - 关键词输入
   - 创建确认

5. 批量操作功能：
   - 批量删除
   - 批量导出
   - 批量状态更新

**交互设计**：
- 加载状态显示
- 错误提示处理
- 成功操作反馈
- 确认对话框

**质量要求**：
- 表格性能优化
- 搜索响应快速
- 操作反馈及时
- 数据展示准确
```

### Task 3.7: 数据分析页面开发

```
请帮我开发微博爬虫项目的数据分析页面：

**项目背景**：
- 展示NLP分析结果
- 提供数据洞察和趋势分析
- 支持多维度数据对比

**具体需求**：
1. 页面布局 (src/pages/Analytics.tsx)：
   ```tsx
   export const Analytics: React.FC = () => {
     const [timeRange, setTimeRange] = useState<TimeRange>('7d');
     const [platform, setPlatform] = useState<string>('all');
     
     return (
       <div className="space-y-6">
         <AnalyticsFilters 
           timeRange={timeRange}
           platform={platform}
           onTimeRangeChange={setTimeRange}
           onPlatformChange={setPlatform}
         />
         <AnalyticsCharts filters={{ timeRange, platform }} />
       </div>
     );
   };
   ```

2. 情感分析图表：
   - 情感分布饼图
   - 情感趋势折线图
   - 平台情感对比

3. 词云组件：
   - 热门关键词展示
   - 交互式词云
   - 词频统计

4. 趋势分析：
   - 话题热度趋势
   - 用户参与度趋势
   - 平台活跃度对比

5. 数据筛选器：
   - 时间范围选择器
   - 平台选择器
   - 话题分类筛选

**图表库集成**：
- 使用Recharts进行数据可视化
- 支持图表交互
- 响应式图表设计
- 数据导出功能

**质量要求**：
- 图表渲染性能好
- 数据更新及时
- 交互体验流畅
- 视觉效果美观
```

## 🔧 开发指南

### React开发最佳实践
- 使用函数组件和Hooks
- 实现组件懒加载
- 优化渲染性能
- 遵循单一职责原则
- 实现错误边界

### TypeScript使用规范
- 严格类型检查
- 避免使用any类型
- 定义完整的接口
- 使用泛型提高复用性
- 实现类型守卫

### 性能优化策略
- 使用React.memo优化组件
- 实现虚拟滚动
- 图片懒加载
- 代码分割和懒加载
- 缓存API响应

## 📝 验收标准

### 功能验收
- [ ] 所有页面正确渲染
- [ ] 用户交互功能正常
- [ ] 数据展示准确
- [ ] 表单验证完整

### 性能验收
- [ ] 页面加载时间 < 3秒
- [ ] 交互响应时间 < 200ms
- [ ] 图表渲染流畅
- [ ] 内存使用合理

### 质量验收
- [ ] 代码质量符合标准
- [ ] TypeScript类型检查通过
- [ ] 组件测试覆盖充分
- [ ] 无障碍访问支持

## 🚨 注意事项

1. **用户体验**：确保界面友好，操作直观
2. **性能优化**：注意大数据量的渲染性能
3. **响应式设计**：适配不同屏幕尺寸
4. **错误处理**：提供友好的错误提示
5. **安全性**：防止XSS攻击，验证用户输入
