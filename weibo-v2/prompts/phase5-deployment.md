# Phase 5: 部署上线 - Augment Code 提示词

## 🎯 阶段目标
完成生产环境部署，建立监控体系，确保系统稳定运行

## 📋 核心任务提示词

### Task 5.1: 生产环境准备

```
请帮我准备微博爬虫项目的生产环境基础设施：

**项目背景**：
- 部署到生产服务器环境
- 需要高可用和高性能配置
- 确保安全性和稳定性

**具体需求**：
1. 服务器环境配置：
   ```bash
   # 系统更新和基础软件安装
   sudo apt update && sudo apt upgrade -y
   sudo apt install -y docker.io docker-compose nginx certbot
   
   # Docker配置
   sudo systemctl enable docker
   sudo systemctl start docker
   sudo usermod -aG docker $USER
   ```

2. PostgreSQL生产配置：
   ```yaml
   # docker-compose.prod.yml
   postgres:
     image: postgres:15
     environment:
       POSTGRES_DB: spider_prod
       POSTGRES_USER: ${DB_USER}
       POSTGRES_PASSWORD: ${DB_PASSWORD}
     volumes:
       - postgres_data:/var/lib/postgresql/data
       - ./postgresql.conf:/etc/postgresql/postgresql.conf
     command: postgres -c config_file=/etc/postgresql/postgresql.conf
     restart: unless-stopped
   ```

3. Redis生产配置：
   ```yaml
   redis:
     image: redis:7-alpine
     command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
     volumes:
       - redis_data:/data
     restart: unless-stopped
   ```

4. RabbitMQ生产配置：
   ```yaml
   rabbitmq:
     image: rabbitmq:3-management
     environment:
       RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
       RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
     volumes:
       - rabbitmq_data:/var/lib/rabbitmq
     restart: unless-stopped
   ```

5. Nginx负载均衡配置：
   ```nginx
   upstream spider_backend {
       server backend1:3000 weight=1;
       server backend2:3000 weight=1;
   }
   
   server {
       listen 80;
       server_name your-domain.com;
       
       location /api/ {
           proxy_pass http://spider_backend;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
       
       location / {
           root /var/www/html;
           try_files $uri $uri/ /index.html;
       }
   }
   ```

**安全配置**：
- 防火墙规则配置
- SSL证书安装
- 数据库访问控制
- 服务间网络隔离

**质量要求**：
- 服务高可用配置
- 数据持久化保证
- 网络安全防护
- 性能优化配置
```

### Task 5.2: 容器化部署配置

```
请帮我配置微博爬虫项目的容器化部署：

**项目背景**：
- 使用Docker进行容器化部署
- 支持多环境配置
- 实现自动化部署流程

**具体需求**：
1. 后端服务Dockerfile：
   ```dockerfile
   # backend/Dockerfile
   FROM rust:1.75 as builder
   
   WORKDIR /app
   COPY Cargo.toml Cargo.lock ./
   COPY src ./src
   
   RUN cargo build --release
   
   FROM debian:bookworm-slim
   
   RUN apt-get update && apt-get install -y \
       ca-certificates \
       libssl3 \
       && rm -rf /var/lib/apt/lists/*
   
   COPY --from=builder /app/target/release/spider-backend /usr/local/bin/
   
   EXPOSE 3000
   
   CMD ["spider-backend"]
   ```

2. 前端服务Dockerfile：
   ```dockerfile
   # frontend/Dockerfile
   FROM node:18-alpine as builder
   
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci
   
   COPY . .
   RUN npm run build
   
   FROM nginx:alpine
   
   COPY --from=builder /app/dist /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/nginx.conf
   
   EXPOSE 80
   
   CMD ["nginx", "-g", "daemon off;"]
   ```

3. 生产环境Docker Compose：
   ```yaml
   version: '3.8'
   
   services:
     backend:
       build: ./backend
       environment:
         - DATABASE_URL=${DATABASE_URL}
         - REDIS_URL=${REDIS_URL}
         - RABBITMQ_URL=${RABBITMQ_URL}
       depends_on:
         - postgres
         - redis
         - rabbitmq
       restart: unless-stopped
       deploy:
         replicas: 2
         resources:
           limits:
             memory: 1G
             cpus: '0.5'
   
     frontend:
       build: ./frontend
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./ssl:/etc/nginx/ssl
       restart: unless-stopped
   ```

4. 健康检查配置：
   ```yaml
   healthcheck:
     test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
     interval: 30s
     timeout: 10s
     retries: 3
     start_period: 40s
   ```

5. 容器资源限制：
   - 内存限制配置
   - CPU使用限制
   - 磁盘空间管理
   - 网络带宽控制

**部署脚本**：
```bash
#!/bin/bash
# deploy.sh

set -e

echo "Starting deployment..."

# 拉取最新代码
git pull origin main

# 构建镜像
docker-compose -f docker-compose.prod.yml build

# 停止旧服务
docker-compose -f docker-compose.prod.yml down

# 启动新服务
docker-compose -f docker-compose.prod.yml up -d

echo "Deployment completed!"
```

**质量要求**：
- 镜像构建优化
- 容器启动快速
- 健康检查有效
- 资源使用合理
```

### Task 5.3: 数据库迁移

```
请帮我执行微博爬虫项目的数据库迁移：

**项目背景**：
- 从现有数据库迁移到新系统
- 确保数据完整性和一致性
- 支持回滚和恢复机制

**具体需求**：
1. 数据库备份脚本：
   ```bash
   #!/bin/bash
   # backup.sh
   
   BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"
   mkdir -p $BACKUP_DIR
   
   # 备份数据库
   pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $BACKUP_DIR/database.sql
   
   # 备份文件
   tar -czf $BACKUP_DIR/files.tar.gz /app/uploads
   
   echo "Backup completed: $BACKUP_DIR"
   ```

2. 数据迁移脚本：
   ```rust
   // src/migrations/migration.rs
   use sea_orm_migration::prelude::*;
   
   #[derive(DeriveMigrationName)]
   pub struct Migration;
   
   #[async_trait::async_trait]
   impl MigrationTrait for Migration {
       async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
           // 添加新字段，不删除现有字段
           manager
               .alter_table(
                   Table::alter()
                       .table(Alias::new("spilder_topic"))
                       .add_column(
                           ColumnDef::new(Alias::new("crawl_status"))
                               .integer()
                               .default(0)
                       )
                       .to_owned(),
               )
               .await?;
           
           Ok(())
       }
   
       async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
           manager
               .alter_table(
                   Table::alter()
                       .table(Alias::new("spilder_topic"))
                       .drop_column(Alias::new("crawl_status"))
                       .to_owned(),
               )
               .await?;
           
           Ok(())
       }
   }
   ```

3. 数据验证脚本：
   ```rust
   // src/utils/data_validator.rs
   pub async fn validate_migration(db: &DatabaseConnection) -> Result<(), ValidationError> {
       // 验证记录数量
       let old_count = get_old_record_count().await?;
       let new_count = topic::Entity::find().count(db).await?;
       
       if old_count != new_count {
           return Err(ValidationError::RecordCountMismatch);
       }
       
       // 验证数据完整性
       validate_data_integrity(db).await?;
       
       // 验证关联关系
       validate_relationships(db).await?;
       
       Ok(())
   }
   ```

4. 回滚机制：
   ```bash
   #!/bin/bash
   # rollback.sh
   
   BACKUP_FILE=$1
   
   if [ -z "$BACKUP_FILE" ]; then
       echo "Usage: $0 <backup_file>"
       exit 1
   fi
   
   # 停止应用服务
   docker-compose down
   
   # 恢复数据库
   psql -h $DB_HOST -U $DB_USER -d $DB_NAME < $BACKUP_FILE
   
   # 启动旧版本服务
   docker-compose -f docker-compose.old.yml up -d
   
   echo "Rollback completed"
   ```

**迁移步骤**：
1. 创建完整数据备份
2. 执行数据库结构迁移
3. 验证数据完整性
4. 更新应用配置
5. 启动新版本服务
6. 验证系统功能

**质量要求**：
- 数据迁移零丢失
- 迁移过程可回滚
- 验证机制完整
- 迁移时间最小化
```

### Task 5.4: 蓝绿部署实施

```
请帮我实施微博爬虫项目的蓝绿部署策略：

**项目背景**：
- 实现零停机升级
- 支持快速回滚
- 确保服务连续性

**具体需求**：
1. 蓝绿环境配置：
   ```yaml
   # docker-compose.blue.yml
   version: '3.8'
   services:
     backend-blue:
       build: ./backend
       environment:
         - ENV=production
         - COLOR=blue
       ports:
         - "3001:3000"
       networks:
         - blue-network
   
     frontend-blue:
       build: ./frontend
       ports:
         - "8001:80"
       networks:
         - blue-network
   
   # docker-compose.green.yml
   version: '3.8'
   services:
     backend-green:
       build: ./backend
       environment:
         - ENV=production
         - COLOR=green
       ports:
         - "3002:3000"
       networks:
         - green-network
   ```

2. 负载均衡器配置：
   ```nginx
   upstream backend_blue {
       server backend-blue:3000;
   }
   
   upstream backend_green {
       server backend-green:3000;
   }
   
   upstream backend_active {
       server backend-blue:3000;  # 当前活跃环境
   }
   
   server {
       listen 80;
       location /api/ {
           proxy_pass http://backend_active;
       }
   }
   ```

3. 部署脚本：
   ```bash
   #!/bin/bash
   # blue-green-deploy.sh
   
   CURRENT_COLOR=$(get_current_color)
   NEW_COLOR=$([ "$CURRENT_COLOR" = "blue" ] && echo "green" || echo "blue")
   
   echo "Current environment: $CURRENT_COLOR"
   echo "Deploying to: $NEW_COLOR"
   
   # 部署到新环境
   docker-compose -f docker-compose.$NEW_COLOR.yml up -d
   
   # 健康检查
   wait_for_health_check $NEW_COLOR
   
   # 切换流量
   switch_traffic $NEW_COLOR
   
   # 停止旧环境
   docker-compose -f docker-compose.$CURRENT_COLOR.yml down
   
   echo "Deployment completed: $NEW_COLOR is now active"
   ```

4. 健康检查函数：
   ```bash
   wait_for_health_check() {
       local color=$1
       local port=$([ "$color" = "blue" ] && echo "3001" || echo "3002")
       
       for i in {1..30}; do
           if curl -f http://localhost:$port/health; then
               echo "Health check passed for $color environment"
               return 0
           fi
           sleep 10
       done
       
       echo "Health check failed for $color environment"
       return 1
   }
   ```

5. 流量切换脚本：
   ```bash
   switch_traffic() {
       local new_color=$1
       
       # 更新Nginx配置
       sed -i "s/backend_active/backend_$new_color/g" /etc/nginx/sites-available/default
       
       # 重新加载Nginx
       nginx -s reload
       
       echo "Traffic switched to $new_color environment"
   }
   ```

**回滚机制**：
- 快速流量切换
- 自动故障检测
- 一键回滚功能
- 状态监控告警

**质量要求**：
- 部署过程零停机
- 健康检查准确
- 回滚机制可靠
- 监控告警及时
```

### Task 5.5: 监控系统搭建

```
请帮我搭建微博爬虫项目的监控系统：

**项目背景**：
- 实时监控系统状态
- 收集性能指标
- 提供告警通知

**具体需求**：
1. Prometheus配置：
   ```yaml
   # prometheus.yml
   global:
     scrape_interval: 15s
     evaluation_interval: 15s
   
   rule_files:
     - "alert_rules.yml"
   
   scrape_configs:
     - job_name: 'spider-backend'
       static_configs:
         - targets: ['backend:3000']
       metrics_path: '/metrics'
       scrape_interval: 10s
   
     - job_name: 'postgres'
       static_configs:
         - targets: ['postgres-exporter:9187']
   
     - job_name: 'redis'
       static_configs:
         - targets: ['redis-exporter:9121']
   ```

2. Grafana仪表板配置：
   ```json
   {
     "dashboard": {
       "title": "Spider System Monitoring",
       "panels": [
         {
           "title": "API Response Time",
           "type": "graph",
           "targets": [
             {
               "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
               "legendFormat": "95th percentile"
             }
           ]
         },
         {
           "title": "Database Connections",
           "type": "stat",
           "targets": [
             {
               "expr": "pg_stat_database_numbackends",
               "legendFormat": "Active Connections"
             }
           ]
         }
       ]
     }
   }
   ```

3. 应用指标收集：
   ```rust
   // src/metrics.rs
   use prometheus::{Counter, Histogram, Gauge, register_counter, register_histogram, register_gauge};
   
   lazy_static! {
       pub static ref HTTP_REQUESTS_TOTAL: Counter = register_counter!(
           "http_requests_total",
           "Total number of HTTP requests"
       ).unwrap();
       
       pub static ref HTTP_REQUEST_DURATION: Histogram = register_histogram!(
           "http_request_duration_seconds",
           "HTTP request duration in seconds"
       ).unwrap();
       
       pub static ref ACTIVE_SPIDERS: Gauge = register_gauge!(
           "active_spiders_total",
           "Number of active spider instances"
       ).unwrap();
   }
   ```

4. 告警规则配置：
   ```yaml
   # alert_rules.yml
   groups:
     - name: spider_alerts
       rules:
         - alert: HighResponseTime
           expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
           for: 5m
           labels:
             severity: warning
           annotations:
             summary: "High API response time"
             description: "95th percentile response time is {{ $value }}s"
   
         - alert: DatabaseConnectionHigh
           expr: pg_stat_database_numbackends > 80
           for: 2m
           labels:
             severity: critical
           annotations:
             summary: "High database connection count"
   ```

5. 日志聚合配置：
   ```yaml
   # docker-compose.monitoring.yml
   loki:
     image: grafana/loki:latest
     ports:
       - "3100:3100"
     volumes:
       - ./loki-config.yml:/etc/loki/local-config.yaml
   
   promtail:
     image: grafana/promtail:latest
     volumes:
       - /var/log:/var/log:ro
       - ./promtail-config.yml:/etc/promtail/config.yml
   ```

**监控指标**：
- 系统资源使用率
- API响应时间和错误率
- 数据库连接和查询性能
- 爬虫任务执行状态
- 消息队列积压情况

**质量要求**：
- 监控数据准确
- 告警及时有效
- 仪表板直观清晰
- 历史数据保留
```

### Task 5.6: 文档和培训

```
请帮我编写微博爬虫项目的运维文档和进行系统培训：

**项目背景**：
- 为运维团队提供完整的操作指南
- 确保系统可维护性
- 建立知识传承体系

**具体需求**：
1. 系统部署文档：
   ```markdown
   # 微博爬虫系统部署指南
   
   ## 环境要求
   - Ubuntu 20.04 LTS
   - Docker 20.10+
   - Docker Compose 2.0+
   - 最小配置：4核8GB内存，100GB存储
   
   ## 部署步骤
   1. 克隆代码仓库
   2. 配置环境变量
   3. 执行部署脚本
   4. 验证系统功能
   
   ## 配置说明
   - 数据库连接配置
   - Redis缓存配置
   - RabbitMQ队列配置
   - 爬虫参数配置
   ```

2. 运维操作手册：
   ```markdown
   # 运维操作手册
   
   ## 日常维护
   - 系统状态检查
   - 日志文件清理
   - 数据库备份
   - 性能监控
   
   ## 常见问题处理
   - 服务启动失败
   - 数据库连接异常
   - 爬虫任务堆积
   - 内存使用过高
   
   ## 应急处理流程
   - 系统故障响应
   - 数据恢复流程
   - 服务回滚操作
   - 联系方式和升级路径
   ```

3. 故障排查指南：
   ```markdown
   # 故障排查指南
   
   ## 系统监控检查
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 查看服务日志
   docker-compose logs -f backend
   
   # 检查系统资源
   htop
   df -h
   ```
   
   ## 常见故障处理
   - 数据库连接超时
   - Redis内存不足
   - 爬虫被反爬虫机制阻止
   - API响应时间过长
   ```

4. 备份恢复流程：
   ```markdown
   # 备份恢复流程
   
   ## 自动备份
   - 数据库每日自动备份
   - 配置文件版本控制
   - 日志文件定期归档
   
   ## 手动备份
   ```bash
   # 创建数据库备份
   ./scripts/backup.sh
   
   # 备份应用配置
   tar -czf config-backup.tar.gz config/
   ```
   
   ## 数据恢复
   ```bash
   # 恢复数据库
   ./scripts/restore.sh backup-20240101.sql
   
   # 恢复配置文件
   tar -xzf config-backup.tar.gz
   ```
   ```

5. 培训材料：
   - 系统架构介绍
   - 部署流程演示
   - 监控系统使用
   - 故障处理实践
   - Q&A环节

**培训计划**：
- 系统概述培训（2小时）
- 部署操作培训（4小时）
- 监控运维培训（3小时）
- 故障处理培训（3小时）
- 实践操作培训（4小时）

**质量要求**：
- 文档内容完整准确
- 操作步骤清晰明确
- 培训效果良好
- 知识传承有效
```

## 🔧 部署指南

### 部署前检查
- 服务器资源充足
- 网络连接稳定
- 安全策略配置
- 备份机制就绪

### 部署流程
1. 环境准备和配置
2. 数据库迁移和验证
3. 应用服务部署
4. 监控系统配置
5. 功能验证测试

### 上线后监控
- 系统性能监控
- 错误日志监控
- 用户访问监控
- 业务指标监控

## 📝 验收标准

### 部署验收
- [ ] 生产环境配置正确
- [ ] 服务正常启动运行
- [ ] 数据库迁移成功
- [ ] 监控系统工作正常

### 性能验收
- [ ] 系统响应时间达标
- [ ] 并发处理能力充足
- [ ] 资源使用合理
- [ ] 稳定性表现良好

### 运维验收
- [ ] 文档完整清晰
- [ ] 培训效果良好
- [ ] 故障处理流程完善
- [ ] 备份恢复机制有效

## 🚨 注意事项

1. **安全性**：确保生产环境安全配置
2. **稳定性**：验证系统长期稳定运行
3. **可维护性**：建立完善的运维体系
4. **监控告警**：及时发现和处理问题
5. **文档更新**：保持文档与系统同步
