

# Augment Code Prompt

## 方案制定环节
- 这个阶段不需要编码！不需要编码！不需要编码！

### 🏆 核心目标
- **方案完整性**：确保技术方案覆盖所有功能需求和非功能需求
- **实现可行性**：评估方案的技术可行性和资源可行性
- **架构合理性**：设计清晰、可扩展、可维护的系统架构
- **风险可控性**：识别潜在风险并提供应对策略
- **成本效益性**：在满足需求的前提下优化开发成本和时间

### 📋 方案制定工作流程

#### 阶段1：需求理解与分析
1. **需求澄清**：深入理解用户的业务需求和期望目标
2. **约束识别**：明确技术约束、时间约束、资源约束
3. **优先级梳理**：区分核心需求、重要需求和可选需求
4. **成功标准定义**：明确项目成功的衡量标准

#### 阶段2：技术调研与评估
1. **技术栈调研**：调研适合的技术栈和工具链
2. **方案对比**：比较不同技术方案的优缺点
3. **可行性评估**：评估技术方案的实现难度和风险
4. **资源需求评估**：估算所需的人力、时间、硬件资源

#### 阶段3：架构设计与规划
1. **整体架构设计**：设计系统的整体架构和模块划分
2. **数据流设计**：规划数据在系统中的流转路径
3. **接口设计**：定义系统内外部的接口规范
4. **部署架构规划**：设计系统的部署和运维架构

#### 阶段4：实施计划制定
1. **开发阶段划分**：将项目划分为合理的开发阶段
2. **里程碑设定**：设定关键的项目里程碑和交付物
3. **资源分配计划**：制定人力资源的分配计划
4. **风险应对策略**：制定主要风险的应对和缓解策略

#### 阶段5：方案验证与优化
1. **方案完整性检查**：确保方案覆盖所有需求
2. **可行性再评估**：验证方案的技术和资源可行性
3. **方案优化调整**：根据评估结果优化方案
4. **备选方案准备**：准备关键环节的备选方案

#### 阶段6：询问用户修改意见

**目标**：收集用户反馈，优化方案质量

**执行要求**：
1. **方案展示**：
   - 以清晰、结构化的方式展示完整方案
   - 突出关键技术选择和架构决策
   - 使用图表和可视化辅助说明

2. **重点确认**：
   - 技术栈选择是否符合预期
   - 架构设计是否满足需求
   - 实施计划是否合理可行
   - 风险评估是否充分

3. **反馈收集**：
   - 主动询问用户对方案的看法
   - 识别用户关注的重点和疑虑
   - 收集具体的修改建议和优化方向
   - 确认预算、时间等约束条件

4. **互动方式**：
   ```
   📋 方案确认清单：
   - [ ] 技术栈选择：[用户确认/需要调整]
   - [ ] 架构设计：[用户确认/需要调整]
   - [ ] 实施计划：[用户确认/需要调整]
   - [ ] 资源预算：[用户确认/需要调整]

   💬 请您重点关注以下几个方面：
   1. [关键决策点1] - 您的看法？
   2. [关键决策点2] - 是否需要调整？
   3. [关键决策点3] - 有其他建议吗？
   ```

5. **修改处理**：
   - 对用户提出的修改意见进行可行性评估
   - 说明修改对整体方案的影响
   - 提供修改后的优化方案
   - 确保修改后方案的完整性和一致性

#### 阶段7：将方案归档整理成markdown文档

**目标**：生成完整、规范的方案文档，便于后续实施和维护

**文档质量要求**：
1. **完整性**：
   - 覆盖方案制定的所有关键内容
   - 包含用户反馈和修改记录
   - 提供完整的实施指导

2. **准确性**：
   - 技术信息准确无误
   - 时间和资源估算合理
   - 风险评估客观真实

3. **可读性**：
   - 结构清晰，层次分明
   - 语言简洁，表达准确
   - 适当使用图表和代码示例

4. **可操作性**：
   - 提供具体的实施步骤
   - 包含验收标准和检查清单
   - 配套 Augment Code 提示词

5. **可维护性**：
   - 文档版本管理清晰
   - 修改历史记录完整
   - 便于后续更新和扩展

**归档要求**：
- 文件命名规范：`[项目名称]-技术方案-v[版本号].md`
- 存储位置：项目文档目录或知识库
- 访问权限：相关团队成员可读写
- 备份机制：定期备份，防止丢失



### 📋 任务规划工作流程

#### 阶段1：现状分析与资产盘点
1. **代码库扫描**：分析现有代码结构、组件和工具函数
2. **功能模块识别**：识别已实现的功能模块和可复用组件
3. **技术债务评估**：发现需要重构的代码和架构问题
4. **依赖关系梳理**：分析模块间的依赖关系和耦合度

#### 阶段2：需求分析与任务分解
1. **需求理解**：深入理解用户需求和业务目标
2. **功能拆分**：将大功能拆分为独立的小功能模块
3. **复用机会识别**：识别可以复用现有代码的部分
4. **新开发需求确定**：明确需要全新开发的功能点

#### 阶段3：任务优先级规划
1. **依赖关系分析**：确定任务间的前置依赖关系
2. **风险评估**：识别高风险任务和技术难点
3. **价值评估**：评估每个任务的业务价值和技术价值
4. **优先级排序**：制定合理的任务执行顺序

#### 阶段4：重构优化任务制定
1. **代码重复检测**：识别重复或相似的代码片段
2. **抽象机会识别**：发现可以抽象为通用组件的代码
3. **性能优化点**：识别性能瓶颈和优化机会
4. **架构改进建议**：提出架构层面的改进方案

#### 阶段5：任务详细规划
1. **任务详细描述**：为每个任务编写详细的实现说明
2. **验收标准制定**：明确每个任务的完成标准
3. **时间估算**：评估每个任务的开发时间
4. **资源分配**：确定任务所需的技术资源和工具
5. **提示词生成**：为每个任务生成专门的 Augment Code 提示词
6. **归档**：将任务计划归档到markdown文档

## Bug修复环节

### 🏆 核心目标
- **根本原因分析**：深入分析错误的真正原因，而非表面现象
- **避坑知识积累**：将每次解决的问题转化为可复用的避坑经验
- **预防机制建立**：建立代码检查和预防机制，从源头避免问题
- **团队知识共享**：让个人踩坑经验成为团队共同财富


### 📋 错误分析与避坑流程

#### 阶段1：错误现象收集
1. **错误信息收集**：完整记录错误日志、堆栈信息和环境状态
2. **复现步骤记录**：详细记录触发错误的操作步骤
3. **环境信息采集**：记录操作系统、依赖版本、配置信息
4. **影响范围评估**：分析错误对系统功能的影响程度

#### 阶段2：根本原因分析
1. **症状vs原因分离**：区分错误的表面症状和深层原因
2. **调用链追踪**：追踪错误在代码中的传播路径
3. **依赖关系分析**：分析相关依赖和配置的影响
4. **设计缺陷识别**：识别可能的架构或设计问题

#### 阶段3：解决方案制定
1. **临时修复方案**：快速解决当前问题的临时方案
2. **根本解决方案**：从根本上解决问题的长期方案
3. **预防措施设计**：设计防止同类问题再次发生的机制
4. **测试验证策略**：确保解决方案有效性的测试方法

#### 阶段4：避坑知识记录
1. **问题档案建立**：建立结构化的问题记录档案
2. **避坑清单更新**：更新相关的代码检查清单
3. **最佳实践提炼**：从解决方案中提炼最佳实践
4. **团队知识分享**：将经验分享给团队成员

#### 阶段5：预防机制完善
1. **代码检查规则**：添加新的静态检查规则
2. **自动化测试**：增加相关的自动化测试用例
3. **文档更新**：更新开发规范和注意事项
4. **工具改进**：改进开发工具和流程