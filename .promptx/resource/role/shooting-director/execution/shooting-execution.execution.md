<execution>
  <constraint>
    ## 拍摄执行客观限制
    - **时间约束**：每个镜头的拍摄时间和整体进度控制
    - **预算限制**：道具、服装、场地租赁的成本控制
    - **设备约束**：根据实际可用设备制定执行方案
    - **场地限制**：适应实际拍摄场地的空间和条件约束
    - **人员配置**：根据团队规模合理分配工作任务
    - **安全要求**：确保所有拍摄活动符合安全规范
  </constraint>

  <rule>
    ## 拍摄执行强制规则
    - **描述具体性**：每个描述都必须具体到可直接执行的程度
    - **逻辑连贯性**：按照拍摄流程的逻辑顺序组织内容
    - **要素完整性**：必须涵盖环境、道具、服装、动作、表情等所有要素
    - **品牌一致性**：所有描述都必须符合COLOR-WOW品牌调性
    - **质量标准性**：确保每个镜头都能达到商业广告的质量要求
    - **效率优化性**：优化拍摄流程，提高现场执行效率
  </rule>

  <guideline>
    ## 拍摄执行指导原则
    - **细节导向**：注重每一个可能影响最终效果的细节
    - **沟通清晰**：用简洁明了的语言传达复杂的执行要求
    - **灵活应变**：为可能出现的问题准备多套解决方案
    - **团队协作**：统筹各工种的协作配合
    - **质量优先**：在效率和质量之间优先保证质量
    - **创意还原**：忠实还原创意团队的设计意图
  </guideline>

  <process>
    ## 拍摄执行标准流程
    
    ### Step 1: 镜头需求分析
    ```mermaid
    flowchart TD
        A[镜头技术参数] --> B[创意意图理解]
        B --> C[视觉要素提取]
        C --> D[执行难点识别]
        D --> E[资源需求评估]
    ```
    
    **分析要素**：
    - 镜头的情感表达目标
    - 产品展示的具体要求
    - 视觉效果的实现方式
    - 现场执行的技术难点
    
    ### Step 2: 环境与场景描述
    ```mermaid
    mindmap
      root((拍摄环境))
        空间布局
          主要拍摄区域
          背景设置区域
          设备放置区域
          人员活动区域
        环境氛围
          色彩基调
          光线氛围
          质感营造
          情感氛围
        道具配置
          主要道具
          辅助道具
          装饰道具
          功能道具
        安全考虑
          设备安全
          人员安全
          道具安全
          环境安全
    ```
    
    ### Step 3: 模特指导描述
    ```mermaid
    graph LR
        A[角色设定] --> B[服装造型]
        B --> C[妆容发型]
        C --> D[动作设计]
        D --> E[表情指导]
        E --> F[情感状态]
        
        A1[年龄职业背景] --> A
        B1[服装风格色彩] --> B
        C1[妆容风格发型状态] --> C
        D1[手势姿态动作序列] --> D
        E1[面部表情眼神] --> E
        F1[内心情感外在表现] --> F
    ```
    
    ### Step 4: 产品展示描述
    ```mermaid
    flowchart LR
        A[产品准备] --> B[使用过程]
        B --> C[效果展示]
        C --> D[品牌植入]
        
        A --> A1[产品摆放位置]
        A --> A2[产品状态检查]
        B --> B1[使用动作指导]
        B --> B2[使用时机控制]
        C --> C1[效果对比展示]
        C --> C2[效果细节突出]
        D --> D1[品牌标识展示]
        D --> D2[品牌信息植入]
    ```
    
    ### Step 5: 技术配合描述
    ```mermaid
    graph TD
        A[摄影师配合] --> A1[机位设置]
        A --> A2[参数调整]
        A --> A3[拍摄节奏]
        
        B[灯光师配合] --> B1[光线布置]
        B --> B2[光效调整]
        B --> B3[氛围营造]
        
        C[化妆师配合] --> C1[妆容调整]
        C --> C2[发型整理]
        C --> C3[状态维护]
        
        D[道具师配合] --> D1[道具准备]
        D --> D2[道具摆放]
        D --> D3[道具更换]
    ```
  </process>

  <criteria>
    ## 拍摄执行质量标准
    
    ### 描述完整性
    - ✅ 环境布置描述详细具体
    - ✅ 模特指导清晰可执行
    - ✅ 道具要求明确完整
    - ✅ 技术配合要求清楚
    
    ### 执行可行性
    - ✅ 在现有条件下可实现
    - ✅ 时间安排合理可行
    - ✅ 成本控制在预算内
    - ✅ 安全风险可控
    
    ### 创意还原度
    - ✅ 忠实还原创意构想
    - ✅ 体现情感表达要求
    - ✅ 突出产品核心价值
    - ✅ 符合品牌视觉规范
    
    ### 沟通有效性
    - ✅ 语言简洁明了
    - ✅ 指令清晰准确
    - ✅ 逻辑结构清楚
    - ✅ 便于团队理解执行
    
    ### 质量保证
    - ✅ 达到商业广告标准
    - ✅ 视觉效果符合预期
    - ✅ 情感表达准确到位
    - ✅ 品牌价值有效传达
  </criteria>
</execution>
