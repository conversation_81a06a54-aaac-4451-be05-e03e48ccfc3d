<execution>
  <constraint>
    ## 故事结构客观限制
    - **时长约束**：广告视频通常15-60秒，故事结构必须紧凑
    - **注意力限制**：观众注意力集中时间短，必须快速抓住兴趣点
    - **信息密度**：有限时间内要传达品牌信息、产品功能、情感价值
    - **平台适配**：不同平台对视频格式和时长有不同要求
  </constraint>

  <rule>
    ## 故事架构强制规则
    - **三幕结构必须**：建立-发展-高潮，每个广告都要有完整故事弧
    - **5秒法则**：前5秒必须抓住观众注意力
    - **反转点强制**：每个故事都必须有明确的情感或认知反转点
    - **品牌植入自然**：品牌出现必须与故事情节自然融合
    - **行动召唤明确**：结尾必须有清晰的行动指引
  </rule>

  <guideline>
    ## 故事设计指导原则
    - **情感驱动**：用情感而非逻辑推动故事发展
    - **冲突制造**：通过冲突和对比增强戏剧张力
    - **细节真实**：用真实的生活细节增强可信度
    - **节奏控制**：合理分配紧张和舒缓的节奏
    - **视觉叙事**：优先用画面而非对白讲故事
  </guideline>

  <process>
    ## 故事架构设计流程
    
    ### Step 1: 核心冲突识别
    ```mermaid
    mindmap
      root((核心冲突))
        内在冲突
          自信 vs 不安
          期望 vs 现实
          理想 vs 妥协
        外在冲突
          时间紧迫
          环境限制
          他人期待
        产品冲突
          问题 vs 解决方案
          普通 vs 专业
          复杂 vs 简单
    ```
    
    ### Step 2: 三幕结构设计
    ```mermaid
    graph LR
        A[第一幕<br/>建立情境<br/>5-10秒] --> B[第二幕<br/>冲突发展<br/>20-30秒]
        B --> C[第三幕<br/>解决高潮<br/>10-15秒]
        
        A1[人物介绍<br/>环境设定<br/>问题暗示] --> A
        B1[冲突加剧<br/>情感铺垫<br/>反转准备] --> B
        C1[问题解决<br/>情感释放<br/>品牌植入] --> C
    ```
    
    ### Step 3: 情感曲线设计
    ```mermaid
    graph TD
        A[平静开始] --> B[问题出现]
        B --> C[情绪低点]
        C --> D[转机出现]
        D --> E[反转高潮]
        E --> F[满意结局]
        
        style A fill:#e1f5fe
        style C fill:#ffebee
        style E fill:#e8f5e9
        style F fill:#fff3e0
    ```
    
    ### Step 4: 反转点精确设计
    
    **认知反转模式**：
    ```mermaid
    flowchart LR
        A[观众以为A] --> B[实际是B]
        B --> C[重新理解前面内容]
        C --> D[产生惊喜感]
    ```
    
    **情感反转模式**：
    ```mermaid
    flowchart LR
        A[负面情绪积累] --> B[转折点触发]
        B --> C[正面情绪爆发]
        C --> D[情感满足感]
    ```
    
    **场景反转模式**：
    ```mermaid
    flowchart LR
        A[普通场景] --> B[意外元素介入]
        B --> C[场景完全改变]
        C --> D[新的故事可能]
    ```
    
    ### Step 5: 视觉叙事节奏
    ```mermaid
    gantt
        title 60秒广告视觉节奏示例
        dateFormat X
        axisFormat %s
        
        section 建立阶段
        环境建立    :0, 5
        人物介绍    :3, 8
        问题暗示    :6, 12
        
        section 发展阶段
        冲突加剧    :12, 25
        情感铺垫    :20, 35
        反转准备    :30, 40
        
        section 高潮阶段
        反转触发    :40, 45
        情感爆发    :43, 50
        品牌植入    :48, 55
        行动召唤    :53, 60
    ```
    
    ### Step 6: 多版本故事架构
    
    **15秒版本**：
    ```
    建立(3秒) → 冲突(5秒) → 反转(4秒) → 品牌(3秒)
    ```
    
    **30秒版本**：
    ```
    建立(6秒) → 发展(12秒) → 反转(8秒) → 品牌(4秒)
    ```
    
    **60秒版本**：
    ```
    建立(12秒) → 发展(25秒) → 高潮(15秒) → 品牌(8秒)
    ```
  </process>

  <criteria>
    ## 故事质量评价标准
    
    ### 结构完整性
    - ✅ 三幕结构清晰完整
    - ✅ 情感曲线起伏合理
    - ✅ 节奏控制恰到好处
    - ✅ 时长分配科学合理
    
    ### 冲突设计
    - ✅ 核心冲突明确突出
    - ✅ 冲突与产品功能相关
    - ✅ 冲突解决方式自然
    - ✅ 情感张力足够强烈
    
    ### 反转效果
    - ✅ 反转点设置巧妙
    - ✅ 反转逻辑合理可信
    - ✅ 反转带来惊喜感
    - ✅ 反转强化品牌价值
    
    ### 视觉叙事
    - ✅ 画面信息传达清晰
    - ✅ 视觉元素支持故事
    - ✅ 镜头语言运用恰当
    - ✅ 视觉节奏与情感同步
    
    ### 品牌整合
    - ✅ 品牌植入自然流畅
    - ✅ 产品功能展示清晰
    - ✅ 品牌价值传达有力
    - ✅ 行动召唤明确有效
  </criteria>
</execution>
